import 'package:flutter/material.dart';
import 'package:fishing_app/config/pocketbase_config.dart';

/// 调试用户记录的工具类
/// 用于检查和修复用户记录中的问题
class DebugUserRecord {
  
  /// 检查指定用户的记录完整性
  static Future<void> checkUserRecord(String userId) async {
    try {
      debugPrint('=== 开始检查用户记录 ===');
      debugPrint('用户ID: $userId');
      
      // 获取用户记录
      final record = await pb.collection('users').getOne(userId);
      
      debugPrint('用户记录原始数据:');
      debugPrint('  完整数据: ${record.toJson()}');
      debugPrint('  data字段: ${record.data}');
      
      // 检查关键字段
      final username = record.data['username'];
      final email = record.data['email'];
      final nickname = record.data['nickname'];
      
      debugPrint('关键字段检查:');
      debugPrint('  username: $username (类型: ${username.runtimeType})');
      debugPrint('  email: $email (类型: ${email.runtimeType})');
      debugPrint('  nickname: $nickname (类型: ${nickname.runtimeType})');
      
      // 检查字段是否为空
      final usernameEmpty = username == null || username.toString().trim().isEmpty;
      final emailEmpty = email == null || email.toString().trim().isEmpty;
      final nicknameEmpty = nickname == null || nickname.toString().trim().isEmpty;
      
      debugPrint('字段空值检查:');
      debugPrint('  username为空: $usernameEmpty');
      debugPrint('  email为空: $emailEmpty');
      debugPrint('  nickname为空: $nicknameEmpty');
      
      if (usernameEmpty || emailEmpty || nicknameEmpty) {
        debugPrint('⚠️ 发现空字段，这可能是导致更新失败的原因');
      } else {
        debugPrint('✅ 所有必需字段都有值');
      }
      
      debugPrint('=== 用户记录检查完成 ===');
      
    } catch (e) {
      debugPrint('❌ 检查用户记录失败: $e');
    }
  }
  
  /// 尝试修复用户记录中的空字段
  static Future<bool> fixUserRecord(String userId) async {
    try {
      debugPrint('=== 开始修复用户记录 ===');
      
      // 获取当前记录
      final record = await pb.collection('users').getOne(userId);
      
      // 检查并修复空字段
      final updates = <String, dynamic>{};
      
      if (record.data['username'] == null || record.data['username'].toString().trim().isEmpty) {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        updates['username'] = 'user_$timestamp';
        debugPrint('修复username: ${updates['username']}');
      }
      
      if (record.data['email'] == null || record.data['email'].toString().trim().isEmpty) {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        updates['email'] = 'user_$<EMAIL>';
        debugPrint('修复email: ${updates['email']}');
      }
      
      if (record.data['nickname'] == null || record.data['nickname'].toString().trim().isEmpty) {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        updates['nickname'] = '用户$timestamp';
        debugPrint('修复nickname: ${updates['nickname']}');
      }
      
      if (updates.isNotEmpty) {
        debugPrint('执行修复更新: $updates');
        await pb.collection('users').update(userId, body: updates);
        debugPrint('✅ 用户记录修复成功');
        return true;
      } else {
        debugPrint('✅ 用户记录无需修复');
        return true;
      }
      
    } catch (e) {
      debugPrint('❌ 修复用户记录失败: $e');
      return false;
    }
  }
  
  /// 测试更新操作
  static Future<bool> testUpdate(String userId) async {
    try {
      debugPrint('=== 测试用户记录更新 ===');
      
      await pb.collection('users').update(userId, body: {
        'lastLoginAt': DateTime.now().toIso8601String(),
      });
      
      debugPrint('✅ 更新测试成功');
      return true;
      
    } catch (e) {
      debugPrint('❌ 更新测试失败: $e');
      return false;
    }
  }
}
