#!/bin/bash

# 测试图片上传的脚本
# 用于验证AWS S3签名修复是否有效

echo "🔍 开始测试图片上传..."

# 检查PocketBase是否运行
if ! curl -s http://localhost:8090/api/health > /dev/null; then
    echo "❌ PocketBase服务器未运行，请先启动PocketBase"
    exit 1
fi

echo "✅ PocketBase服务器正在运行"

# 测试AWS签名算法
echo "🔍 测试AWS签名算法..."
SIGNATURE_TEST=$(curl -s http://localhost:8090/api/test/signature)
echo "签名测试结果: $SIGNATURE_TEST"

# 测试签名组件
echo "🔍 测试签名组件..."
COMPONENT_TEST=$(curl -s http://localhost:8090/api/test/signature/components)
echo "组件测试结果: $COMPONENT_TEST"

echo "🔍 测试完成"
