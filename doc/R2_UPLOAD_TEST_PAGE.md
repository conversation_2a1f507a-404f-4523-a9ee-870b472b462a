# R2图片上传测试页面

## 概述

这是一个专门用于测试Cloudflare R2图片上传功能的调试页面。该页面实现了完整的AWS S3 Signature V4算法，在客户端直接生成签名并上传图片到R2存储。

## 功能特性

### ✅ 已实现功能

1. **客户端AWS S3签名生成**
   - 完整的AWS S3 Signature V4算法实现
   - 使用Dart的crypto库进行HMAC-SHA256计算
   - 正确的规范请求和待签名字符串构建

2. **图片处理**
   - 图片选择（从相册）
   - 自动压缩和优化
   - 实时预览

3. **R2上传**
   - 预签名URL生成
   - 直接PUT上传到R2
   - 上传进度和状态显示

4. **调试功能**
   - 详细的调试日志
   - 实时状态更新
   - 错误信息显示
   - 上传成功后的图片预览

## 访问方式

1. **启动应用**（确保开发模式已启用）
2. **找到Debug菜单**（橙色bug图标）
3. **选择"R2上传测试"**

## 配置信息

测试页面使用`key.md`文件中的R2凭据：

```dart
// R2配置（来自key.md）
static const String _accessKeyId = '2975b9f0b5ed91f29bec884c3fdcd4c8';
static const String _secretAccessKey = '****************************************************************';
static const String _endpoint = 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com';
static const String _bucketName = 'fishing-app';
static const String _region = 'auto';
```

## 使用流程

### 1. 选择图片
- 点击"选择图片"按钮
- 从相册中选择一张图片
- 图片会自动显示预览

### 2. 上传图片
- 点击"上传到R2"按钮
- 系统会自动：
  - 压缩图片（最大1920px，质量85%）
  - 生成唯一的对象键
  - 计算AWS S3签名
  - 上传到R2存储

### 3. 查看结果
- 上传成功后会显示图片URL
- 可以点击"查看上传的图片"预览
- 调试日志显示详细的处理过程

## 技术实现

### AWS S3 Signature V4算法

```dart
// 1. 构建规范请求
final canonicalRequest = [
  'PUT',
  canonicalUri,
  canonicalQueryString,
  canonicalHeaders,
  signedHeaders,
  payloadHash,
].join('\n');

// 2. 构建待签名字符串
final stringToSign = [
  'AWS4-HMAC-SHA256',
  amzDate,
  credentialScope,
  hashedCanonicalRequest,
].join('\n');

// 3. 计算签名
final signature = _calculateSignature(stringToSign, dateStamp);
```

### HMAC链式计算

```dart
String _calculateSignature(String stringToSign, String dateStamp) {
  var key = utf8.encode('AWS4$_secretAccessKey');
  
  // HMAC链式计算
  var kDate = Hmac(sha256, key).convert(utf8.encode(dateStamp)).bytes;
  var kRegion = Hmac(sha256, kDate).convert(utf8.encode(_region)).bytes;
  var kService = Hmac(sha256, kRegion).convert(utf8.encode('s3')).bytes;
  var kSigning = Hmac(sha256, kService).convert(utf8.encode('aws4_request')).bytes;
  
  // 最终签名
  var signature = Hmac(sha256, kSigning).convert(utf8.encode(stringToSign));
  
  return signature.toString();
}
```

## 调试信息

测试页面提供详细的调试日志，**所有调试信息都会同时显示在页面上和打印到控制台**：

### 页面显示
- 🔍 **处理步骤**：图片选择、压缩、签名生成
- ✅ **成功操作**：各步骤完成状态
- ❌ **错误信息**：详细的错误描述
- 🔍 **技术细节**：时间戳、签名、URL等

### 控制台输出
所有日志都会以`[R2测试]`前缀打印到Flutter控制台，包括：

- **基本流程日志**：通过`_addLog()`方法自动打印
- **详细技术信息**：直接使用`debugPrint()`打印
  - 完整的规范请求内容
  - 完整的待签名字符串
  - HMAC链式计算的每一步结果
  - 完整的预签名URL
  - HTTP请求和响应详情

### 查看控制台日志
在Flutter开发环境中运行应用时，可以在终端或IDE的控制台中看到所有调试信息：

```bash
flutter run
# 然后在应用中使用R2上传测试功能
# 所有调试信息会实时显示在控制台
```

### 控制台日志示例

```
[R2测试] [12:34:56] 🔍 已选择图片: test.jpg
[R2测试] [12:34:56] 🔍 开始处理图片...
[R2测试] [12:34:57] 🔍 图片处理完成，压缩后大小: 245678 bytes
[R2测试] [12:34:57] 🔍 对象键: test-uploads/client-signed-1234567890.jpg
[R2测试] [12:34:57] 🔍 开始生成AWS S3签名...
🔍 [R2测试] 时间戳: 20250721T123457Z
🔍 [R2测试] 日期戳: 20250721
🔍 [R2测试] 规范URI: /fishing-app/test-uploads/client-signed-1234567890.jpg
🔍 [R2测试] 规范查询字符串: X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...
🔍 [R2测试] 完整规范请求:
PUT
/fishing-app/test-uploads/client-signed-1234567890.jpg
X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...
host:ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com

host
UNSIGNED-PAYLOAD
🔍 [R2测试] 规范请求哈希: abc123def456...
🔍 [R2测试] 凭据范围: 20250721/auto/s3/aws4_request
🔍 [R2测试] 完整待签名字符串:
AWS4-HMAC-SHA256
20250721T123457Z
20250721/auto/s3/aws4_request
abc123def456...
🔍 [R2测试] 开始HMAC-SHA256链式计算
🔍 [R2测试] 初始密钥长度: 68
🔍 [R2测试] kDate: 1234567890abcdef...
🔍 [R2测试] kRegion: abcdef1234567890...
🔍 [R2测试] kService: 567890abcdef1234...
🔍 [R2测试] kSigning: def1234567890abc...
🔍 [R2测试] 最终签名: 1234567890abcdef1234567890abcdef12345678
🔍 [R2测试] 签名计算完成: 1234567890abcdef1234567890abcdef12345678
🔍 [R2测试] 签名长度: 64
🔍 [R2测试] 最终预签名URL: https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com/fishing-app/test-uploads/client-signed-1234567890.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&...
[R2测试] [12:34:57] 🔍 预签名URL生成成功
[R2测试] [12:34:57] 🔍 开始上传到R2...
[R2测试] [12:34:57] 🔍 发送PUT请求到R2...
[R2测试] [12:34:57] 🔍 文件大小: 245678 bytes
🔍 [R2测试] 预签名URL: https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com/fishing-app/test-uploads/...
[R2测试] [12:34:58] 🔍 响应状态码: 200
[R2测试] [12:34:58] 🔍 响应头: {content-length: 0, date: Mon, 21 Jul 2025 12:34:58 GMT, ...}
[R2测试] [12:34:58] ✅ 上传成功！图片URL: https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com/fishing-app/test-uploads/client-signed-1234567890.jpg
```

## 对比测试

这个客户端实现可以与PocketBase服务器端的签名生成进行对比：

1. **客户端签名**：使用标准Dart crypto库
2. **服务器端签名**：使用PocketBase的$security.hs256()

通过对比可以验证哪种实现与AWS S3标准更兼容。

## 故障排除

### 常见问题

1. **签名不匹配**
   - 检查时间戳是否正确
   - 验证HMAC计算是否标准
   - 确认查询参数顺序

2. **上传失败**
   - 检查网络连接
   - 验证R2凭据
   - 查看详细错误信息

3. **图片处理失败**
   - 确认图片格式支持
   - 检查文件大小限制
   - 验证权限设置

### 调试建议

1. **查看调试日志**：所有操作都有详细日志
2. **对比服务器端**：与PocketBase实现对比
3. **网络抓包**：查看实际HTTP请求
4. **R2控制台**：检查存储桶状态

## 文件结构

```
lib/pages/debug/
└── r2_upload_test_page.dart    # 主测试页面

lib/widgets/
└── dev_menu.dart              # 开发菜单（已更新）

doc/
└── R2_UPLOAD_TEST_PAGE.md     # 本文档
```

## 依赖项

测试页面使用以下依赖：

- `image_picker`: 图片选择
- `image`: 图片处理和压缩
- `crypto`: HMAC-SHA256计算
- `http`: HTTP请求

所有依赖都已在`pubspec.yaml`中配置。
