# R2图片上传测试页面

## 概述

这是一个专门用于测试Cloudflare R2图片上传功能的调试页面。该页面实现了完整的AWS S3 Signature V4算法，在客户端直接生成签名并上传图片到R2存储。

## 功能特性

### ✅ 已实现功能

1. **客户端AWS S3签名生成**
   - 完整的AWS S3 Signature V4算法实现
   - 使用Dart的crypto库进行HMAC-SHA256计算
   - 正确的规范请求和待签名字符串构建

2. **图片处理**
   - 图片选择（从相册）
   - 自动压缩和优化
   - 实时预览

3. **R2上传**
   - 预签名URL生成
   - 直接PUT上传到R2
   - 上传进度和状态显示

4. **调试功能**
   - 详细的调试日志
   - 实时状态更新
   - 错误信息显示
   - 上传成功后的图片预览

## 访问方式

1. **启动应用**（确保开发模式已启用）
2. **找到Debug菜单**（橙色bug图标）
3. **选择"R2上传测试"**

## 配置信息

测试页面使用`key.md`文件中的R2凭据：

```dart
// R2配置（来自key.md）
static const String _accessKeyId = '2975b9f0b5ed91f29bec884c3fdcd4c8';
static const String _secretAccessKey = '****************************************************************';
static const String _endpoint = 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com';
static const String _bucketName = 'fishing-app';
static const String _region = 'auto';
```

## 使用流程

### 1. 选择图片
- 点击"选择图片"按钮
- 从相册中选择一张图片
- 图片会自动显示预览

### 2. 上传图片
- 点击"上传到R2"按钮
- 系统会自动：
  - 压缩图片（最大1920px，质量85%）
  - 生成唯一的对象键
  - 计算AWS S3签名
  - 上传到R2存储

### 3. 查看结果
- 上传成功后会显示图片URL
- 可以点击"查看上传的图片"预览
- 调试日志显示详细的处理过程

## 技术实现

### AWS S3 Signature V4算法

```dart
// 1. 构建规范请求
final canonicalRequest = [
  'PUT',
  canonicalUri,
  canonicalQueryString,
  canonicalHeaders,
  signedHeaders,
  payloadHash,
].join('\n');

// 2. 构建待签名字符串
final stringToSign = [
  'AWS4-HMAC-SHA256',
  amzDate,
  credentialScope,
  hashedCanonicalRequest,
].join('\n');

// 3. 计算签名
final signature = _calculateSignature(stringToSign, dateStamp);
```

### HMAC链式计算

```dart
String _calculateSignature(String stringToSign, String dateStamp) {
  var key = utf8.encode('AWS4$_secretAccessKey');
  
  // HMAC链式计算
  var kDate = Hmac(sha256, key).convert(utf8.encode(dateStamp)).bytes;
  var kRegion = Hmac(sha256, kDate).convert(utf8.encode(_region)).bytes;
  var kService = Hmac(sha256, kRegion).convert(utf8.encode('s3')).bytes;
  var kSigning = Hmac(sha256, kService).convert(utf8.encode('aws4_request')).bytes;
  
  // 最终签名
  var signature = Hmac(sha256, kSigning).convert(utf8.encode(stringToSign));
  
  return signature.toString();
}
```

## 调试信息

测试页面提供详细的调试日志，包括：

- 🔍 **处理步骤**：图片选择、压缩、签名生成
- ✅ **成功操作**：各步骤完成状态
- ❌ **错误信息**：详细的错误描述
- 🔍 **技术细节**：时间戳、签名、URL等

## 对比测试

这个客户端实现可以与PocketBase服务器端的签名生成进行对比：

1. **客户端签名**：使用标准Dart crypto库
2. **服务器端签名**：使用PocketBase的$security.hs256()

通过对比可以验证哪种实现与AWS S3标准更兼容。

## 故障排除

### 常见问题

1. **签名不匹配**
   - 检查时间戳是否正确
   - 验证HMAC计算是否标准
   - 确认查询参数顺序

2. **上传失败**
   - 检查网络连接
   - 验证R2凭据
   - 查看详细错误信息

3. **图片处理失败**
   - 确认图片格式支持
   - 检查文件大小限制
   - 验证权限设置

### 调试建议

1. **查看调试日志**：所有操作都有详细日志
2. **对比服务器端**：与PocketBase实现对比
3. **网络抓包**：查看实际HTTP请求
4. **R2控制台**：检查存储桶状态

## 文件结构

```
lib/pages/debug/
└── r2_upload_test_page.dart    # 主测试页面

lib/widgets/
└── dev_menu.dart              # 开发菜单（已更新）

doc/
└── R2_UPLOAD_TEST_PAGE.md     # 本文档
```

## 依赖项

测试页面使用以下依赖：

- `image_picker`: 图片选择
- `image`: 图片处理和压缩
- `crypto`: HMAC-SHA256计算
- `http`: HTTP请求

所有依赖都已在`pubspec.yaml`中配置。
