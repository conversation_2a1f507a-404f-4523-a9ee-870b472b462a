// AWS S3 Signature V4 修复实现
// 基于标准算法，不依赖PocketBase的HMAC实现

// 测试使用最新失败数据的AWS签名
routerAdd("GET", "/api/test/aws-signature-fix", (c) => {
  console.log('收到AWS签名修复测试请求');

  try {
    // 使用最新失败的实际数据
    const testCase = {
      accessKeyId: '2975b9f0b5ed91f29bec884c3fdcd4c8',
      secretAccessKey: '****************************************************************',
      region: 'auto',
      dateStamp: '20250721',
      amzDate: '20250721T124917Z'
    };
    
    // 使用R2提供的实际规范请求
    const canonicalRequest = `PUT
/fishing-app/spots/h9t1py1t2w0hk6l/xfs4fv9lrxsbvjh/1753102157883_mdd3rebf5dh3c.jpeg
X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2975b9f0b5ed91f29bec884c3fdcd4c8%2F20250721%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250721T124917Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host
host:ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com

host
UNSIGNED-PAYLOAD`;
    
    // 计算规范请求哈希
    const hashedCanonicalRequest = $security.sha256(canonicalRequest);
    console.log('规范请求哈希:', hashedCanonicalRequest);
    
    // 构建待签名字符串（使用R2提供的实际数据）
    const credentialScope = `${testCase.dateStamp}/${testCase.region}/s3/aws4_request`;
    const r2StringToSign = `AWS4-HMAC-SHA256
20250721T124917Z
20250721/auto/s3/aws4_request
8bd91647d2b6099e75943672898692627fe5f79cbc7384550b35e2bb7a0a7b6a`;

    const ourStringToSign = `AWS4-HMAC-SHA256
${testCase.amzDate}
${credentialScope}
${hashedCanonicalRequest}`;
    
    console.log('我们的待签名字符串:', ourStringToSign);
    console.log('R2的待签名字符串:', r2StringToSign);
    console.log('StringToSign匹配:', ourStringToSign === r2StringToSign);
    
    // 尝试不同的HMAC方法
    console.log('开始HMAC链计算...');
    
    // 方法1：使用PocketBase的HMAC计算R2的StringToSign
    const kDate1 = $security.hs256(testCase.dateStamp, 'AWS4' + testCase.secretAccessKey);
    const kRegion1 = $security.hs256(testCase.region, kDate1);
    const kService1 = $security.hs256('s3', kRegion1);
    const kSigning1 = $security.hs256('aws4_request', kService1);
    const signature1 = $security.hs256(r2StringToSign, kSigning1);
    
    console.log('PocketBase HMAC结果:');
    console.log('- kDate:', kDate1);
    console.log('- signature:', signature1);
    
    // 方法2：尝试使用不同的密钥格式
    // 假设问题在于十六进制字符串需要转换为二进制
    function hexToString(hex) {
      let str = '';
      for (let i = 0; i < hex.length; i += 2) {
        str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
      }
      return str;
    }
    
    // 尝试将十六进制结果转换为字符串再用作密钥
    const kDate2 = $security.hs256(testCase.dateStamp, 'AWS4' + testCase.secretAccessKey);
    const kRegion2 = $security.hs256(testCase.region, hexToString(kDate2));
    const kService2 = $security.hs256('s3', hexToString(kRegion2));
    const kSigning2 = $security.hs256('aws4_request', hexToString(kService2));
    const signature2 = $security.hs256(r2StringToSign, hexToString(kSigning2));
    
    console.log('十六进制转换方法结果:');
    console.log('- signature:', signature2);
    
    // R2提供的我们的签名和期望的哈希
    const r2ExpectedHash = '8bd91647d2b6099e75943672898692627fe5f79cbc7384550b35e2bb7a0a7b6a';
    const r2ProvidedSignature = '0546a11d8037c781f5df9069a19c0ef590902af0a3bac888327977de24032f49';
    
    const testResult = {
      message: '实际失败数据的AWS签名测试',
      testCase: testCase,
      canonicalRequestHash: {
        ours: hashedCanonicalRequest,
        r2Expected: r2ExpectedHash,
        matches: hashedCanonicalRequest === r2ExpectedHash
      },
      stringToSignMatches: ourStringToSign === r2StringToSign,
      signatures: {
        r2Provided: r2ProvidedSignature,
        pocketbaseMethod: signature1,
        hexConvertMethod: signature2,
        pocketbaseMatches: signature1 === r2ProvidedSignature,
        hexConvertMatches: signature2 === r2ProvidedSignature
      },
      analysis: {
        problem: 'PocketBase的$security.hs256()实现与AWS标准HMAC-SHA256不兼容',
        evidence: '规范请求哈希正确，但HMAC链计算结果不匹配',
        solution: '需要使用Cloudflare Worker或其他标准HMAC实现'
      }
    };
    
    console.log('AWS签名修复测试结果:', testResult);
    
    return c.json(200, {
      success: true,
      message: 'AWS签名修复测试完成',
      result: testResult
    });
    
  } catch (error) {
    console.error('AWS签名修复测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || 'AWS签名修复测试失败'
    });
  }
});

// 创建一个使用修复方法的预签名URL生成器
routerAdd("POST", "/api/generate-upload-url-fixed", (c) => {
  console.log('收到修复版预签名URL生成请求');
  
  try {
    const data = $apis.requestInfo(c).data;
    const { spotId, fileName, fileType } = data;
    
    if (!spotId || !fileName || !fileType) {
      return c.json(400, {
        success: false,
        error: '缺少必要参数: spotId, fileName, fileType'
      });
    }
    
    // 这里我们可以实现一个修复版的签名生成
    // 但首先需要确定正确的HMAC实现方法
    
    return c.json(200, {
      success: true,
      message: '修复版预签名URL生成器（开发中）',
      note: '需要先确定正确的HMAC实现方法'
    });
    
  } catch (error) {
    console.error('修复版预签名URL生成失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '修复版预签名URL生成失败'
    });
  }
});
