// PocketBase Hook - 完整的AWS S3 Signature V4实现
// 用于生成Cloudflare R2预签名上传URL

// R2配置 - 更新于2025-07-21
const R2_CONFIG = {
  endpoint: 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com',
  bucketName: 'fishing-app',
  region: 'auto',
  // 注意：这些密钥应该从环境变量或安全存储获取
  accessKeyId: '2975b9f0b5ed91f29bec884c3fdcd4c8',
  secretAccessKey: '****************************************************************'
};

// 工具函数定义 - 使用var声明确保函数提升
console.log('🔧 开始定义工具函数...');

/**
 * 生成短UUID
 * @returns {string} 短UUID
 */
var generateShortUuid = function() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
};

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名
 */
var getFileExtension = function(filename) {
  const parts = filename.split('.');
  return parts.length > 1 ? parts.pop().toLowerCase() : 'jpg';
};

/**
 * 格式化AMZ日期
 * @param {Date} date - 日期对象
 * @returns {string} 格式化的日期字符串
 */
var formatAmzDate = function(date) {
  return date.toISOString().replace(/[:\-]|\.\d{3}/g, '');
};

/**
 * 从端点URL获取主机名
 * @param {string} endpoint - 端点URL
 * @returns {string} 主机名
 */
var getHostFromEndpoint = function(endpoint) {
  return endpoint.replace(/^https?:\/\//, '');
};

console.log('✅ 工具函数定义完成');

// 获取R2上传令牌的API端点
routerAdd("GET", "/api/get-r2-token", (e) => {
  console.log('收到获取R2令牌请求');

  try {
    // 验证用户身份
    const authRecord = e.auth;
    if (!authRecord) {
      return e.json(401, { error: "未授权访问" });
    }

    // R2配置
    const r2Config = {
      endpoint: 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com',
      bucketName: 'fishing-app',
      accessKeyId: '2975b9f0b5ed91f29bec884c3fdcd4c8',
      secretAccessKey: '****************************************************************',
      region: 'auto'
    };

    console.log('✅ [R2令牌] 为用户提供R2访问令牌:', authRecord.id);

    return e.json(200, {
      endpoint: r2Config.endpoint,
      bucketName: r2Config.bucketName,
      accessKeyId: r2Config.accessKeyId,
      secretAccessKey: r2Config.secretAccessKey,
      region: r2Config.region,
      userId: authRecord.id,
      expiresIn: 3600 // 1小时有效期
    });

  } catch (error) {
    console.error('获取R2令牌失败:', error);
    return e.json(500, { error: error.message || '服务器内部错误' });
  }
});

// 保存图片URL到钓点的API端点
routerAdd("POST", "/api/save-image-url", (e) => {
  console.log('收到保存图片URL请求');

  try {
    // 验证用户身份
    const authRecord = e.auth;
    if (!authRecord) {
      return e.json(401, { error: "未授权访问" });
    }

    // 获取请求参数
    const requestInfo = e.requestInfo();
    const data = requestInfo.body;

    const spotId = data.spotId;
    const imageUrl = data.imageUrl;
    const thumbnailUrl = data.thumbnailUrl;
    const fileName = data.fileName;

    if (!spotId || !imageUrl) {
      return e.json(400, { error: "缺少必要参数: spotId, imageUrl" });
    }

    // 验证钓点权限
    try {
      const spot = $app.findRecordById("fishing_spots", spotId);
      if (!spot || spot.get("user_id") !== authRecord.id) {
        return e.json(404, { error: "钓点不存在或无权限" });
      }

      // 更新钓点的图片URL
      const currentPhotos = spot.get("photos") || [];
      const newPhoto = {
        url: imageUrl,
        thumbnail_url: thumbnailUrl || imageUrl,
        filename: fileName || 'uploaded_image.jpg',
        uploaded_at: new Date().toISOString()
      };

      currentPhotos.push(newPhoto);
      spot.set("photos", currentPhotos);

      $app.save(spot);

      console.log('✅ [保存图片] 图片URL已保存到钓点:', spotId);

      return e.json(200, {
        success: true,
        message: "图片URL已保存",
        spotId: spotId,
        imageUrl: imageUrl,
        thumbnailUrl: thumbnailUrl
      });

    } catch (spotError) {
      console.error('保存图片URL失败:', spotError);
      return e.json(404, { error: "钓点不存在或保存失败" });
    }

  } catch (error) {
    console.error('保存图片URL异常:', error);
    return e.json(500, { error: error.message || '服务器内部错误' });
  }
});

// 预签名URL端点（保留作为备用）
routerAdd("POST", "/api/generate-upload-url", (e) => {
  console.log('收到预签名URL请求');

  try {
    // 验证用户身份 - 使用正确的PocketBase API
    const authRecord = e.auth;
    const authHeader = e.request.header.get("Authorization");

    console.log('认证记录:', authRecord ? authRecord.id : 'null');
    console.log('Authorization头:', authHeader ? authHeader.substring(0, 20) + '...' : 'null');
    console.log('请求方法:', e.request.method);
    console.log('请求路径:', e.request.url.pathname);

    if (!authRecord) {
      console.log('认证失败，返回401');
      return e.json(401, {
        error: "未授权访问",
        debug: {
          hasAuthRecord: !!authRecord,
          hasAuthHeader: !!authHeader,
          method: e.request.method,
          path: e.request.url.pathname
        }
      });
    }

    // 获取请求参数 - 使用requestInfo方法
    const requestInfo = e.requestInfo();
    const data = requestInfo.body;
    console.log('🔍 [预签名URL] 收到请求');
    console.log('🔍 [预签名URL] 用户ID:', authRecord.id);
    console.log('🔍 [预签名URL] 请求数据:', JSON.stringify(data));

    const spotId = data.spotId;
    const fileName = data.fileName;
    const fileType = data.fileType;
    console.log('🔍 [预签名URL] 解析参数:', { spotId, fileName, fileType });

    // 验证参数
    if (!spotId || !fileName || !fileType) {
      console.log('❌ [预签名URL] 参数验证失败');
      return e.json(400, { error: "缺少必要参数: spotId, fileName, fileType" });
    }

    // 验证钓点是否存在且属于用户 - 使用PocketBase v0.23+的新API
    console.log('🔍 [预签名URL] 开始验证钓点:', spotId);
    try {
      // 使用新的API: $app.findRecordById 而不是 $app.dao().findRecordById
      const spot = $app.findRecordById("fishing_spots", spotId);
      console.log('🔍 [预签名URL] 找到钓点:', spot ? spot.id : 'null');
      console.log('🔍 [预签名URL] 钓点用户ID:', spot ? spot.get("user_id") : 'null');
      console.log('🔍 [预签名URL] 当前用户ID:', authRecord.id);

      if (!spot) {
        console.log('❌ [预签名URL] 钓点不存在');
        return e.json(404, { error: "钓点不存在" });
      }

      if (spot.get("user_id") !== authRecord.id) {
        console.log('❌ [预签名URL] 钓点权限验证失败');
        return e.json(403, { error: "钓点不存在或无权限" });
      }

      console.log('✅ [预签名URL] 钓点验证通过');
    } catch (spotError) {
      console.error('❌ [预签名URL] 查找钓点失败:', spotError);
      console.error('❌ [预签名URL] 错误详情:', spotError.message);
      return e.json(404, { error: "钓点不存在" });
    }

    // 生成文件路径
    const timestamp = Date.now();
    const uuid = timestamp.toString(36) + Math.random().toString(36).substring(2, 7);
    const parts = fileName.split('.');
    const extension = parts.length > 1 ? parts.pop().toLowerCase() : 'jpg';
    const filePath = `spots/${authRecord.id}/${spotId}/${timestamp}_${uuid}.${extension}`;
    const thumbnailPath = `spots/${authRecord.id}/${spotId}/${timestamp}_${uuid}_thumb.${extension}`;

    // 生成预签名URL（1小时有效期，用于调试）
    const expiresIn = 60 * 60; // 1小时

    // R2配置（内联避免作用域问题）- 更新于2025-07-21
    const r2Config = {
      accountId: 'ab76374a6921dbc071ecdda63a033ec1',
      endpoint: 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com',
      bucketName: 'fishing-app',
      region: 'auto',
      accessKeyId: '2975b9f0b5ed91f29bec884c3fdcd4c8',
      secretAccessKey: '****************************************************************'
    };

    console.log('🔧 [预签名URL] 使用简化的预签名URL生成（基于官方文档）');

    // 根据R2服务器反馈实现正确的预签名URL生成
    function generateCorrectPresignedUrl(method, objectKey, expiresIn, config) {
      const now = new Date();
      const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
      const dateStamp = amzDate.substring(0, 8);

      // 构建基础URL
      const baseUrl = `${config.endpoint}/${config.bucketName}/${objectKey}`;
      const host = config.endpoint.replace(/^https?:\/\//, '');

      // 构建查询参数（按字母顺序排序）
      const queryParams = {
        'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
        'X-Amz-Credential': `${config.accessKeyId}/${dateStamp}/${config.region}/s3/aws4_request`,
        'X-Amz-Date': amzDate,
        'X-Amz-Expires': expiresIn.toString(),
        'X-Amz-SignedHeaders': 'host'
      };

      // 构建规范查询字符串
      const canonicalQueryString = Object.keys(queryParams)
        .sort()
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
        .join('&');

      // 构建规范请求
      const canonicalUri = `/${config.bucketName}/${objectKey}`;
      const canonicalHeaders = `host:${host}\n`;
      const signedHeaders = 'host';
      const payloadHash = 'UNSIGNED-PAYLOAD';

      const canonicalRequest = [
        method,
        canonicalUri,
        canonicalQueryString,
        canonicalHeaders,
        signedHeaders,
        payloadHash
      ].join('\n');

      // 构建待签名字符串
      const algorithm = 'AWS4-HMAC-SHA256';
      const credentialScope = `${dateStamp}/${config.region}/s3/aws4_request`;
      const hashedCanonicalRequest = $security.sha256(canonicalRequest);

      const stringToSign = [
        algorithm,
        amzDate,
        credentialScope,
        hashedCanonicalRequest
      ].join('\n');

      const isThumb = objectKey.includes('_thumb');
      const logPrefix = isThumb ? '🖼️ [缩略图签名]' : '🖼️ [原图签名]';

      console.log(`${logPrefix} objectKey:`, objectKey);
      console.log(`${logPrefix} canonicalRequest:`, canonicalRequest);
      console.log(`${logPrefix} hashedCanonicalRequest:`, hashedCanonicalRequest);
      console.log(`${logPrefix} stringToSign:`, stringToSign);

      // 计算签名 - 修复：需要将签名转换为十六进制字符串
      const kDate = $security.hs256(dateStamp, 'AWS4' + config.secretAccessKey);
      const kRegion = $security.hs256(config.region, kDate);
      const kService = $security.hs256('s3', kRegion);
      const kSigning = $security.hs256('aws4_request', kService);
      const signatureBytes = $security.hs256(stringToSign, kSigning);
      
      // 将字节数组转换为十六进制字符串
      const signature = Array.from(signatureBytes)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      console.log(`${logPrefix} 计算的签名:`, signature);

      // 构建最终URL
      queryParams['X-Amz-Signature'] = signature;
      const finalQueryString = Object.keys(queryParams)
        .sort()
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
        .join('&');

      const finalUrl = `${baseUrl}?${finalQueryString}`;
      console.log(`${logPrefix} 最终URL:`, finalUrl);

      return finalUrl;
    }

    // 生成预签名URL
    const presignedUrl = generateCorrectPresignedUrl('PUT', filePath, expiresIn, r2Config);
    const thumbnailPresignedUrl = generateCorrectPresignedUrl('PUT', thumbnailPath, expiresIn, r2Config);

    console.log('✅ [预签名URL] AWS S3签名生成完成');

    // 生成公开访问URL
    const publicUrl = `${r2Config.endpoint}/${r2Config.bucketName}/${filePath}`;
    const thumbnailPublicUrl = `${r2Config.endpoint}/${r2Config.bucketName}/${thumbnailPath}`;

    // 返回完整的预签名URL信息
    return e.json(200, {
      uploadUrl: presignedUrl,
      filePath: filePath,
      publicUrl: publicUrl,
      thumbnailUploadUrl: thumbnailPresignedUrl,
      thumbnailPath: thumbnailPath,
      thumbnailPublicUrl: thumbnailPublicUrl,
      expiresIn: expiresIn,
      timestamp: timestamp
    });

  } catch (error) {
    console.error('生成预签名URL失败:', error);
    return e.json(500, { error: error.message || '服务器内部错误' });
  }
});

// 添加认证测试端点
routerAdd("POST", "/api/test/generate-upload-url-auth", (c) => {
  console.log('收到预签名URL认证测试请求');

  try {
    const authRecord = c.auth;
    const authHeader = c.request.header.get("Authorization");

    console.log('测试认证记录:', authRecord ? authRecord.id : 'null');
    console.log('测试Authorization头:', authHeader ? authHeader.substring(0, 20) + '...' : 'null');

    return c.json(200, {
      success: true,
      message: '认证测试成功',
      authenticated: !!authRecord,
      userId: authRecord ? authRecord.id : null,
      hasAuthHeader: !!authHeader
    });

  } catch (error) {
    console.error('认证测试失败:', error);
    return e.json(500, {
      success: false,
      error: error.message || '认证测试失败'
    });
  }
});

console.log('🚀 AWS S3 Signature V4预签名URL生成服务已加载 - 版本: 2025-07-21-16:30 (修复重复路由)');