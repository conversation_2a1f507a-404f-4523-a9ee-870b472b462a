// PocketBase代理：调用Cloudflare Worker生成预签名URL
// 这是一个临时解决方案，直到找到PocketBase中的HMAC兼容性解决方案

// 代理到Cloudflare Worker的预签名URL生成
routerAdd("POST", "/api/generate-upload-url-worker", (c) => {
  const logPrefix = '🔧 [Worker代理]';
  const CLOUDFLARE_WORKER_URL = 'https://your-worker-name.your-subdomain.workers.dev';

  console.log(`${logPrefix} 收到预签名URL生成请求`);

  try {
    const data = $apis.requestInfo(c).data;
    const { spotId, fileName, fileType } = data;

    if (!spotId || !fileName || !fileType) {
      console.error(`${logPrefix} 缺少必要参数`);
      return c.json(400, {
        success: false,
        error: '缺少必要参数: spotId, fileName, fileType'
      });
    }

    console.log(`${logPrefix} 参数验证通过:`, { spotId, fileName, fileType });

    // 调用Cloudflare Worker
    const workerResponse = $http.send({
      url: CLOUDFLARE_WORKER_URL,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        spotId: spotId,
        fileName: fileName,
        fileType: fileType
      }),
      timeout: 30 // 30秒超时
    });

    if (workerResponse.statusCode !== 200) {
      console.error(`${logPrefix} Worker调用失败:`, workerResponse.statusCode, workerResponse.raw);
      return c.json(500, {
        success: false,
        error: `Worker调用失败: ${workerResponse.statusCode}`
      });
    }

    const workerData = workerResponse.json;
    console.log(`${logPrefix} Worker调用成功`);

    if (!workerData.success) {
      console.error(`${logPrefix} Worker返回错误:`, workerData.error);
      return c.json(500, {
        success: false,
        error: workerData.error || 'Worker处理失败'
      });
    }

    // 返回Worker的响应
    return c.json(200, {
      success: true,
      data: workerData.data,
      source: 'cloudflare-worker',
      note: '使用Cloudflare Worker生成的预签名URL，解决了PocketBase HMAC兼容性问题'
    });

  } catch (error) {
    console.error(`${logPrefix} 代理调用失败:`, error);
    return c.json(500, {
      success: false,
      error: error.message || '代理调用失败'
    });
  }
});

// 测试Worker连接
routerAdd("GET", "/api/test/worker-connection", (c) => {
  const logPrefix = '🔧 [Worker连接测试]';
  const CLOUDFLARE_WORKER_URL = 'https://your-worker-name.your-subdomain.workers.dev';

  console.log(`${logPrefix} 开始测试Worker连接`);

  try {
    // 发送测试请求到Worker
    const testResponse = $http.send({
      url: CLOUDFLARE_WORKER_URL,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        spotId: 'test-spot',
        fileName: 'test.jpg',
        fileType: 'image/jpeg'
      }),
      timeout: 10
    });

    const testResult = {
      workerUrl: CLOUDFLARE_WORKER_URL,
      statusCode: testResponse.statusCode,
      success: testResponse.statusCode === 200,
      response: testResponse.statusCode === 200 ? testResponse.json : testResponse.raw,
      timestamp: new Date().toISOString()
    };

    console.log(`${logPrefix} 测试结果:`, testResult);

    return c.json(200, {
      success: true,
      message: 'Worker连接测试完成',
      result: testResult
    });

  } catch (error) {
    console.error(`${logPrefix} 连接测试失败:`, error);
    return c.json(500, {
      success: false,
      error: error.message || 'Worker连接测试失败'
    });
  }
});

// 使用说明和配置检查
routerAdd("GET", "/api/worker-solution-info", (c) => {
  const CLOUDFLARE_WORKER_URL = 'https://your-worker-name.your-subdomain.workers.dev';

  try {
    const response = {
      success: true,
      message: 'Cloudflare Worker解决方案信息',
      solution: {
        problem: 'PocketBase的$security.hs256()与AWS S3 Signature V4不兼容',
        solution: '使用Cloudflare Worker的标准Web Crypto API生成预签名URL',
        benefits: [
          '完全兼容AWS S3 Signature V4标准',
          '使用官方推荐的aws4fetch库',
          '高性能边缘计算',
          '易于维护和调试'
        ]
      },
      setup: {
        step1: '部署提供的Cloudflare Worker代码',
        step2: '设置Worker环境变量（R2凭证）',
        step3: '更新CLOUDFLARE_WORKER_URL配置',
        step4: '在Flutter应用中调用/api/generate-upload-url-worker'
      },
      currentConfig: {
        workerUrl: CLOUDFLARE_WORKER_URL,
        configured: CLOUDFLARE_WORKER_URL !== 'https://your-worker-name.your-subdomain.workers.dev'
      }
    };

    return c.json(200, response);
  } catch (error) {
    console.error('Worker解决方案信息获取失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || 'Worker解决方案信息获取失败'
    });
  }
});
