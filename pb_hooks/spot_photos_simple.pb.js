// PocketBase Hook - 钓点照片管理（简化版）
// 只包含API路由，暂时不使用Hook函数

// 批量照片上传API
routerAdd("POST", "/api/spots/{spotId}/photos/batch", (e) => {
  console.log('收到批量照片上传请求');

  try {
    const spotId = e.request.pathValue("spotId");
    console.log('钓点ID:', spotId);

    const authRecord = e.auth;
    console.log('认证记录:', authRecord ? authRecord.id : 'null');

    if (!authRecord) {
      return e.json(401, {
        success: false,
        error: "未授权访问"
      });
    }

    // 验证钓点权限
    try {
      const spot = $app.findRecordById("fishing_spots", spotId);
      if (!spot || spot.get("user_id") !== authRecord.id) {
        return e.json(403, {
          success: false,
          error: "钓点不存在或无权限"
        });
      }
    } catch (spotError) {
      console.error('查找钓点失败:', spotError);
      return e.json(404, {
        success: false,
        error: "钓点不存在"
      });
    }

    const requestInfo = e.requestInfo();
    const data = requestInfo.body;
    console.log('请求数据:', JSON.stringify(data));

    const photos = data.photos || [];

    if (!Array.isArray(photos) || photos.length === 0) {
      return e.json(400, {
        success: false,
        error: "照片数据不能为空"
      });
    }

    const results = [];

    // 检查spot_photos集合是否存在
    let collection;
    try {
      collection = $app.findCollectionByNameOrId("spot_photos");
    } catch (collectionError) {
      console.error('spot_photos集合不存在:', collectionError);
      return e.json(500, {
        success: false,
        error: "spot_photos集合不存在，请先创建集合"
      });
    }

    for (let i = 0; i < photos.length; i++) {
      const photo = photos[i];

      // 验证必要字段
      if (!photo.filename || !photo.url || !photo.storage_path) {
        console.error(`照片 ${i} 缺少必要字段:`, photo);
        continue;
      }

      try {
        // 创建照片记录
        const record = new Record(collection);
        record.set("spot_id", spotId);
        record.set("user_id", authRecord.id);
        record.set("filename", photo.filename);
        record.set("url", photo.url);
        record.set("thumbnail_url", photo.thumbnail_url || null);
        record.set("storage_path", photo.storage_path);
        record.set("thumbnail_path", photo.thumbnail_path || null);
        record.set("type", photo.type || "normal");
        record.set("description", photo.description || null);
        record.set("sort_order", photo.sort_order || i);
        record.set("file_size", photo.file_size || null);
        record.set("mime_type", photo.mime_type || null);

        $app.save(record);
        results.push({
          id: record.id,
          filename: record.get("filename"),
          url: record.get("url"),
          sort_order: record.get("sort_order")
        });

        console.log(`批量上传: 照片 ${record.id} 保存成功`);
      } catch (saveError) {
        console.error(`保存照片 ${i} 失败:`, saveError);
      }
    }

    return e.json(200, {
      success: true,
      count: results.length,
      photos: results
    });

  } catch (error) {
    console.error('批量照片上传失败:', error);
    return e.json(500, {
      success: false,
      error: error.message || '服务器内部错误'
    });
  }
});

// 简单的测试API
routerAdd("GET", "/api/test/photos", (e) => {
  return e.json(200, {
    success: true,
    message: "照片API工作正常"
  });
});

// 备用的简化批量照片上传API
routerAdd("POST", "/api/photos/batch", (e) => {
  console.log('收到简化批量照片上传请求');

  try {
    // 调试认证信息
    const authRecord = e.auth;
    const authHeader = e.request.header.get("Authorization");

    console.log('认证记录:', authRecord ? authRecord.id : 'null');
    console.log('Authorization头:', authHeader ? authHeader.substring(0, 20) + '...' : 'null');

    if (!authRecord) {
      console.log('认证失败，返回401');
      return e.json(401, {
        success: false,
        error: "未授权访问",
        debug: {
          hasAuthRecord: !!authRecord,
          hasAuthHeader: !!authHeader
        }
      });
    }

    // 正确解析JSON请求体
    let data;
    try {
      const requestInfo = e.requestInfo();
      data = requestInfo.body;
      console.log('原始数据:', JSON.stringify({
        spotId: data.spotId,
        photosCount: data.photos ? data.photos.length : 0
      }));
    } catch (bindError) {
      console.error('数据绑定失败:', bindError);
      return e.json(400, {
        success: false,
        error: "请求数据格式错误: " + bindError.message
      });
    }

    const spotId = data.spotId;
    const photos = data.photos || [];

    console.log('钓点ID:', spotId);
    console.log('照片数量:', photos.length);
    console.log('照片数据类型:', typeof photos);
    console.log('是否为数组:', Array.isArray(photos));

    if (!spotId || !Array.isArray(photos) || photos.length === 0) {
      return e.json(400, {
        success: false,
        error: "缺少必要参数"
      });
    }

    // 模拟保存成功
    return e.json(200, {
      success: true,
      count: photos.length,
      message: "照片记录保存成功（模拟）"
    });

  } catch (error) {
    console.error('简化批量照片上传失败:', error);
    return e.json(500, {
      success: false,
      error: error.message || '服务器内部错误'
    });
  }
});

console.log('钓点照片管理服务已加载');