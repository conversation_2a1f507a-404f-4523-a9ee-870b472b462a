// 自定义HMAC-SHA256实现，用于解决PocketBase签名兼容性问题

// 测试自定义HMAC实现
routerAdd("GET", "/api/test/custom-hmac", (c) => {
  console.log('收到自定义HMAC测试请求');

  // 将函数定义移到处理器内部以避免作用域问题
  function hexToBytes(hex) {
    const bytes = [];
    for (let i = 0; i < hex.length; i += 2) {
      bytes.push(parseInt(hex.substr(i, 2), 16));
    }
    return bytes;
  }

  function bytesToHex(bytes) {
    return bytes.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  function stringToBytes(str) {
    const bytes = [];
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      if (code < 0x80) {
        bytes.push(code);
      } else if (code < 0x800) {
        bytes.push(0xc0 | (code >> 6));
        bytes.push(0x80 | (code & 0x3f));
      } else if (code < 0x10000) {
        bytes.push(0xe0 | (code >> 12));
        bytes.push(0x80 | ((code >> 6) & 0x3f));
        bytes.push(0x80 | (code & 0x3f));
      }
    }
    return bytes;
  }

  function customHmacSha256(message, key) {
    // 简化版本：直接使用PocketBase的SHA256，但修复HMAC逻辑
    let keyBytes;
    if (typeof key === 'string' && /^[0-9a-f]+$/i.test(key) && key.length === 64) {
      keyBytes = hexToBytes(key);
    } else if (typeof key === 'string') {
      keyBytes = stringToBytes(key);
    } else {
      keyBytes = key;
    }

    const messageBytes = stringToBytes(message);
    const blockSize = 64;

    if (keyBytes.length > blockSize) {
      const hashedKey = $security.sha256(bytesToHex(keyBytes));
      keyBytes = hexToBytes(hashedKey);
    }

    while (keyBytes.length < blockSize) {
      keyBytes.push(0);
    }

    const ipad = keyBytes.map(b => b ^ 0x36);
    const opad = keyBytes.map(b => b ^ 0x5c);

    const innerInput = bytesToHex([...ipad, ...messageBytes]);
    const innerHash = $security.sha256(innerInput);

    const outerInput = bytesToHex([...opad, ...hexToBytes(innerHash)]);
    const outerHash = $security.sha256(outerInput);

    return outerHash;
  }

  try {
    // 使用AWS官方测试用例
    const testCase = {
      accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
      secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
      region: 'us-east-1',
      dateStamp: '20130524'
    };
    
    // 比较PocketBase的HMAC和我们的自定义HMAC
    const message = testCase.dateStamp;
    const key = 'AWS4' + testCase.secretAccessKey;
    
    const pocketbaseHmac = $security.hs256(message, key);
    const customHmac = customHmacSha256(message, key);
    
    // 测试HMAC链
    const kDate1 = $security.hs256(testCase.dateStamp, 'AWS4' + testCase.secretAccessKey);
    const kDate2 = customHmacSha256(testCase.dateStamp, 'AWS4' + testCase.secretAccessKey);
    
    const kRegion1 = $security.hs256(testCase.region, kDate1);
    const kRegion2 = customHmacSha256(testCase.region, kDate2);
    
    const testResult = {
      message: '自定义HMAC测试',
      comparison: {
        pocketbaseHmac: pocketbaseHmac,
        customHmac: customHmac,
        matches: pocketbaseHmac === customHmac
      },
      hmacChain: {
        kDate: {
          pocketbase: kDate1,
          custom: kDate2,
          matches: kDate1 === kDate2
        },
        kRegion: {
          pocketbase: kRegion1,
          custom: kRegion2,
          matches: kRegion1 === kRegion2
        }
      }
    };
    
    console.log('自定义HMAC测试结果:', testResult);
    
    return c.json(200, {
      success: true,
      message: '自定义HMAC测试完成',
      result: testResult
    });
    
  } catch (error) {
    console.error('自定义HMAC测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '自定义HMAC测试失败'
    });
  }
});
