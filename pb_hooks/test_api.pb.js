// 测试API - 验证PocketBase Hook功能

// 简单的测试端点
routerAdd("GET", "/api/test", (e) => {
  console.log('收到测试请求');

  return e.json(200, {
    success: true,
    message: "PocketBase Hook 工作正常",
    timestamp: new Date().toISOString()
  });
});

// 测试认证状态
routerAdd("GET", "/api/test/auth", (e) => {
  console.log('收到认证测试请求');

  const authRecord = e.auth;
  const authHeader = e.request.header.get("Authorization");

  console.log('认证记录:', authRecord ? authRecord.id : 'null');
  console.log('Authorization头:', authHeader ? authHeader.substring(0, 20) + '...' : 'null');

  return e.json(200, {
    success: true,
    authenticated: !!authRecord,
    userId: authRecord ? authRecord.id : null,
    userEmail: authRecord ? authRecord.get("email") : null,
    hasAuthHeader: !!authHeader,
    authHeaderPrefix: authHeader ? authHeader.substring(0, 10) : null
  });
});

// POST版本的认证测试
routerAdd("POST", "/api/test/auth", (e) => {
  console.log('收到POST认证测试请求');

  const authRecord = e.auth;
  const authHeader = e.request.header.get("Authorization");

  console.log('POST认证记录:', authRecord ? authRecord.id : 'null');
  console.log('POST Authorization头:', authHeader ? authHeader.substring(0, 20) + '...' : 'null');

  return e.json(200, {
    success: true,
    authenticated: !!authRecord,
    userId: authRecord ? authRecord.id : null,
    userEmail: authRecord ? authRecord.get("email") : null,
    hasAuthHeader: !!authHeader,
    authHeaderPrefix: authHeader ? authHeader.substring(0, 10) : null
  });
});

// 测试集合是否存在
routerAdd("GET", "/api/test/collections", (e) => {
  console.log('收到集合测试请求');

  return e.json(200, {
    success: true,
    message: "集合测试功能暂时禁用，请使用PocketBase Admin界面检查集合"
  });
});

console.log('测试API已加载');
