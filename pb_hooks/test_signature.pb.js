// 测试AWS S3 Signature V4实现
// 这个文件用于验证签名算法的正确性

// 使用AWS官方测试用例验证我们的签名实现
routerAdd("GET", "/api/test/signature", (c) => {
  console.log('收到AWS官方测试用例验证请求');

  try {
    // 使用AWS官方文档中的测试用例
    // https://docs.aws.amazon.com/AmazonS3/latest/API/sigv4-query-string-auth.html
    const awsTestCase = {
      accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
      secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
      region: 'us-east-1',
      dateStamp: '20130524',
      amzDate: '20130524T000000Z',
      bucketName: 'examplebucket',
      objectKey: 'test.txt',
      method: 'GET',
      expiresIn: 86400
    };

    // 构建AWS测试用例的规范请求
    const canonicalUri = `/${awsTestCase.objectKey}`;
    const credentialScope = `${awsTestCase.dateStamp}/${awsTestCase.region}/s3/aws4_request`;
    const credential = `${awsTestCase.accessKeyId}/${credentialScope}`;
    const canonicalQueryString = `X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=${encodeURIComponent(credential)}&X-Amz-Date=${awsTestCase.amzDate}&X-Amz-Expires=${awsTestCase.expiresIn}&X-Amz-SignedHeaders=host`;
    const canonicalHeaders = `host:${awsTestCase.bucketName}.s3.amazonaws.com\n`;
    const signedHeaders = 'host';
    const payloadHash = 'UNSIGNED-PAYLOAD';

    const canonicalRequest =
      awsTestCase.method + '\n' +
      canonicalUri + '\n' +
      canonicalQueryString + '\n' +
      canonicalHeaders + '\n' +
      signedHeaders + '\n' +
      payloadHash;

    // 计算规范请求哈希
    const hashedCanonicalRequest = $security.sha256(canonicalRequest);

    // 构建待签名字符串
    const stringToSign = [
      'AWS4-HMAC-SHA256',
      awsTestCase.amzDate,
      credentialScope,
      hashedCanonicalRequest
    ].join('\n');

    // 计算签名 - 尝试不同的方法
    console.log('开始HMAC链计算...');

    const secretKey = 'AWS4' + awsTestCase.secretAccessKey;
    console.log('初始密钥:', secretKey);

    const kDate = $security.hs256(awsTestCase.dateStamp, secretKey);
    console.log('kDate:', kDate, '(长度:', kDate.length, ')');

    const kRegion = $security.hs256(awsTestCase.region, kDate);
    console.log('kRegion:', kRegion, '(长度:', kRegion.length, ')');

    const kService = $security.hs256('s3', kRegion);
    console.log('kService:', kService, '(长度:', kService.length, ')');

    const kSigning = $security.hs256('aws4_request', kService);
    console.log('kSigning:', kSigning, '(长度:', kSigning.length, ')');

    const signature = $security.hs256(stringToSign, kSigning);
    console.log('最终签名:', signature, '(长度:', signature.length, ')');

    // AWS官方示例的预期签名（需要查找）
    const expectedSignature = 'aeeed9bbccd4d02ee5c0109b86d86835f995330da4c265957d157751f604d404';

    const testResult = {
      message: 'AWS官方测试用例验证',
      testCase: awsTestCase,
      canonicalRequest: canonicalRequest,
      hashedCanonicalRequest: hashedCanonicalRequest,
      stringToSign: stringToSign,
      hmacChain: {
        kDate: kDate,
        kRegion: kRegion,
        kService: kService,
        kSigning: kSigning
      },
      signatures: {
        calculated: signature,
        expected: expectedSignature,
        matches: signature === expectedSignature
      }
    };

    console.log('AWS官方测试用例验证结果:', testResult);

    return c.json(200, {
      success: true,
      message: 'AWS官方测试用例验证完成',
      result: testResult
    });

  } catch (error) {
    console.error('AWS官方测试用例验证失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || 'AWS官方测试用例验证失败'
    });
  }
});



// 测试$security.hs256函数的返回格式
routerAdd("GET", "/api/test/signature/components", (c) => {
  console.log('收到签名组件测试请求');

  try {
    // 测试$security.hs256的返回格式
    const testMessage = 'test message';
    const testSecret = 'test secret';
    const hmacResult = $security.hs256(testMessage, testSecret);

    console.log('HMAC测试结果:', hmacResult);
    console.log('HMAC结果类型:', typeof hmacResult);
    console.log('HMAC结果长度:', hmacResult.length);
    console.log('HMAC结果是否为字符串:', typeof hmacResult === 'string');

    // 检查是否为十六进制字符串
    const isHexString = /^[0-9a-f]+$/i.test(hmacResult);
    console.log('是否为十六进制字符串:', isHexString);

    const now = new Date();
    const testData = {
      timestamp: now.toISOString(),
      hmacResult: hmacResult,
      hmacType: typeof hmacResult,
      hmacLength: hmacResult.length,
      isHexString: isHexString,
      testMessage: testMessage,
      testSecret: testSecret
    };

    return c.json(200, {
      success: true,
      message: '$security.hs256函数测试成功',
      components: testData
    });

  } catch (error) {
    console.error('签名组件测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '签名组件测试失败'
    });
  }
});

console.log('AWS S3签名测试服务已加载');
