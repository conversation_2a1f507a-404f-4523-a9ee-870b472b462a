// 测试AWS S3 Signature V4实现
// 这个文件用于验证签名算法的正确性

// AWS S3签名V4测试 - 使用AWS官方示例进行验证
routerAdd("GET", "/api/test/signature", (c) => {
  console.log('收到AWS S3签名测试请求');

  try {
    // 使用AWS官方文档中的测试用例
    // https://docs.aws.amazon.com/AmazonS3/latest/API/sigv4-query-string-auth.html
    const testConfig = {
      accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
      secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
      region: 'us-east-1',
      endpoint: 'https://examplebucket.s3.amazonaws.com'
    };

    const testParams = {
      method: 'GET',
      objectKey: 'test.txt',
      timestamp: '20130524T000000Z',
      expiresIn: 86400
    };

    // 预期的签名结果（来自AWS文档）
    const expectedSignature = 'aeeed9bbccd4d02ee5c0109b86d86835f995330da4c265957d157751f604d404';

    // 测试我们的签名实现
    const calculatedSignature = testAwsSignature(testConfig, testParams);

    const testResult = {
      testCase: 'AWS官方示例 - GET预签名URL',
      expected: expectedSignature,
      calculated: calculatedSignature,
      matches: calculatedSignature === expectedSignature,
      testConfig: testConfig,
      testParams: testParams
    };

    console.log('AWS签名测试结果:', testResult);

    return c.json(200, {
      success: true,
      message: 'AWS S3签名测试完成',
      result: testResult
    });

  } catch (error) {
    console.error('AWS签名测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || 'AWS签名测试失败'
    });
  }
});

// 测试AWS签名实现的函数
function testAwsSignature(config, params) {
  const dateStamp = params.timestamp.substring(0, 8);
  const amzDate = params.timestamp;

  // 构建规范请求
  const canonicalRequest = [
    params.method,
    '/' + params.objectKey,
    `X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=${encodeURIComponent(config.accessKeyId + '/' + dateStamp + '/' + config.region + '/s3/aws4_request')}&X-Amz-Date=${amzDate}&X-Amz-Expires=${params.expiresIn}&X-Amz-SignedHeaders=host`,
    'host:examplebucket.s3.amazonaws.com\n',
    'host',
    'UNSIGNED-PAYLOAD'
  ].join('\n');

  console.log('测试规范请求:', canonicalRequest);

  // 构建待签名字符串
  const credentialScope = `${dateStamp}/${config.region}/s3/aws4_request`;
  const hashedCanonicalRequest = $security.sha256(canonicalRequest);
  const stringToSign = [
    'AWS4-HMAC-SHA256',
    amzDate,
    credentialScope,
    hashedCanonicalRequest
  ].join('\n');

  console.log('测试待签名字符串:', stringToSign);

  // 计算签名
  const kDate = $security.hs256(dateStamp, 'AWS4' + config.secretAccessKey);
  const kRegion = $security.hs256(config.region, kDate);
  const kService = $security.hs256('s3', kRegion);
  const kSigning = $security.hs256('aws4_request', kService);
  const signature = $security.hs256(stringToSign, kSigning);

  console.log('测试签名计算步骤:');
  console.log('- kDate:', kDate);
  console.log('- kRegion:', kRegion);
  console.log('- kService:', kService);
  console.log('- kSigning:', kSigning);
  console.log('- signature:', signature);

  return signature;
}

// 测试$security.hs256函数的返回格式
routerAdd("GET", "/api/test/signature/components", (c) => {
  console.log('收到签名组件测试请求');

  try {
    // 测试$security.hs256的返回格式
    const testMessage = 'test message';
    const testSecret = 'test secret';
    const hmacResult = $security.hs256(testMessage, testSecret);

    console.log('HMAC测试结果:', hmacResult);
    console.log('HMAC结果类型:', typeof hmacResult);
    console.log('HMAC结果长度:', hmacResult.length);
    console.log('HMAC结果是否为字符串:', typeof hmacResult === 'string');

    // 检查是否为十六进制字符串
    const isHexString = /^[0-9a-f]+$/i.test(hmacResult);
    console.log('是否为十六进制字符串:', isHexString);

    const now = new Date();
    const testData = {
      timestamp: now.toISOString(),
      hmacResult: hmacResult,
      hmacType: typeof hmacResult,
      hmacLength: hmacResult.length,
      isHexString: isHexString,
      testMessage: testMessage,
      testSecret: testSecret
    };

    return c.json(200, {
      success: true,
      message: '$security.hs256函数测试成功',
      components: testData
    });

  } catch (error) {
    console.error('签名组件测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '签名组件测试失败'
    });
  }
});

console.log('AWS S3签名测试服务已加载');
