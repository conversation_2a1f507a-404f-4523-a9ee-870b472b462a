import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// R2图片上传测试页面
/// 使用key.md中的凭据在客户端直接生成AWS S3签名并上传到R2
class R2UploadTestPage extends StatefulWidget {
  const R2UploadTestPage({super.key});

  @override
  State<R2UploadTestPage> createState() => _R2UploadTestPageState();
}

class _R2UploadTestPageState extends State<R2UploadTestPage> {
  final ImagePicker _picker = ImagePicker();
  
  // R2配置（来自key.md）
  static const String _accessKeyId = '2975b9f0b5ed91f29bec884c3fdcd4c8';
  static const String _secretAccessKey = '****************************************************************';
  static const String _endpoint = 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com';
  static const String _bucketName = 'fishing-app';
  static const String _region = 'auto';
  
  File? _selectedImage;
  bool _isUploading = false;
  String _uploadStatus = '';
  String? _uploadedImageUrl;
  final List<String> _logs = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('R2上传测试'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 配置信息卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'R2配置信息',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Endpoint: $_endpoint'),
                    Text('Bucket: $_bucketName'),
                    Text('Region: $_region'),
                    Text('Access Key: ${_accessKeyId.substring(0, 8)}...'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // 图片选择和预览
            if (_selectedImage != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const Text(
                        '选中的图片',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            _selectedImage!,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isUploading ? null : _pickImage,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('选择图片'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: (_selectedImage != null && !_isUploading) 
                        ? _uploadImage 
                        : null,
                    icon: _isUploading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.cloud_upload),
                    label: Text(_isUploading ? '上传中...' : '上传到R2'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 上传状态
            if (_uploadStatus.isNotEmpty) ...[
              Card(
                color: _uploadStatus.startsWith('✅') 
                    ? Colors.green.shade50 
                    : _uploadStatus.startsWith('❌')
                        ? Colors.red.shade50
                        : Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _uploadStatus,
                    style: TextStyle(
                      color: _uploadStatus.startsWith('✅') 
                          ? Colors.green.shade800 
                          : _uploadStatus.startsWith('❌')
                              ? Colors.red.shade800
                              : Colors.blue.shade800,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 上传成功后显示图片URL
            if (_uploadedImageUrl != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '上传成功！',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('图片URL: $_uploadedImageUrl'),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () => _showUploadedImage(),
                        child: const Text('查看上传的图片'),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 日志区域
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            '调试日志',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: _clearLogs,
                            child: const Text('清除'),
                          ),
                        ],
                      ),
                      const Divider(),
                      Expanded(
                        child: ListView.builder(
                          itemCount: _logs.length,
                          itemBuilder: (context, index) {
                            final log = _logs[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                log,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontFamily: 'monospace',
                                  color: log.startsWith('✅') 
                                      ? Colors.green.shade700
                                      : log.startsWith('❌')
                                          ? Colors.red.shade700
                                          : log.startsWith('🔍')
                                              ? Colors.blue.shade700
                                              : Colors.black87,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 90,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _uploadStatus = '';
          _uploadedImageUrl = null;
        });
        _addLog('🔍 已选择图片: ${image.name}');
      }
    } catch (e) {
      _addLog('❌ 选择图片失败: $e');
    }
  }

  /// 上传图片到R2
  Future<void> _uploadImage() async {
    if (_selectedImage == null) return;

    setState(() {
      _isUploading = true;
      _uploadStatus = '🔍 开始上传图片...';
    });

    try {
      // 1. 读取和处理图片
      _addLog('🔍 开始处理图片...');
      final imageBytes = await _selectedImage!.readAsBytes();
      _addLog('🔍 图片文件大小: ${imageBytes.length} bytes');

      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        throw Exception('无法解码图片，可能是不支持的格式或文件损坏');
      }

      _addLog('🔍 图片解码成功，格式: ${originalImage.format}');

      // 2. 压缩图片 - 添加更安全的处理
      _addLog('🔍 原图尺寸: ${originalImage.width}x${originalImage.height}');

      // 验证图片尺寸
      if (originalImage.width <= 0 || originalImage.height <= 0) {
        throw Exception('图片尺寸无效: ${originalImage.width}x${originalImage.height}');
      }

      if (originalImage.width > 10000 || originalImage.height > 10000) {
        throw Exception('图片尺寸过大: ${originalImage.width}x${originalImage.height}，最大支持10000x10000');
      }

      img.Image compressedImage;
      if (originalImage.width > 1920 || originalImage.height > 1920) {
        // 需要缩放
        final aspectRatio = originalImage.width / originalImage.height;
        int newWidth, newHeight;

        if (originalImage.width > originalImage.height) {
          // 横向图片
          newWidth = 1920;
          newHeight = (1920 / aspectRatio).round();
        } else {
          // 纵向图片
          newHeight = 1920;
          newWidth = (1920 * aspectRatio).round();
        }

        _addLog('🔍 缩放到: ${newWidth}x$newHeight');
        compressedImage = img.copyResize(
          originalImage,
          width: newWidth,
          height: newHeight,
          interpolation: img.Interpolation.linear,
        );
      } else {
        // 不需要缩放，直接使用原图
        _addLog('🔍 图片尺寸合适，不需要缩放');
        compressedImage = originalImage;
      }

      // 编码为JPEG
      List<int> jpegBytes;
      try {
        jpegBytes = img.encodeJpg(compressedImage, quality: 85);
        if (jpegBytes.isEmpty) {
          throw Exception('JPEG编码失败，结果为空');
        }
      } catch (e) {
        throw Exception('JPEG编码失败: $e');
      }

      final compressedBytes = Uint8List.fromList(jpegBytes);
      
      _addLog('🔍 图片处理完成，压缩后大小: ${compressedBytes.length} bytes');

      // 3. 生成对象键
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final objectKey = 'test-uploads/client-signed-$timestamp.jpg';
      _addLog('🔍 对象键: $objectKey');

      // 4. 生成预签名URL
      _addLog('🔍 开始生成AWS S3签名...');
      final presignedUrl = await _generatePresignedUrl(objectKey);
      _addLog('🔍 预签名URL生成成功');

      // 5. 上传到R2
      _addLog('🔍 开始上传到R2...');
      final success = await _uploadToR2(presignedUrl, compressedBytes);

      if (success) {
        final publicUrl = '$_endpoint/$_bucketName/$objectKey';
        setState(() {
          _uploadStatus = '✅ 上传成功！';
          _uploadedImageUrl = publicUrl;
        });
        _addLog('✅ 上传成功！图片URL: $publicUrl');
      } else {
        setState(() {
          _uploadStatus = '❌ 上传失败';
        });
        _addLog('❌ 上传到R2失败');
      }
    } catch (e) {
      setState(() {
        _uploadStatus = '❌ 上传失败: $e';
      });
      _addLog('❌ 上传异常: $e');

      // 如果是图片处理错误，尝试简单处理
      if (e.toString().contains('ImageException') || e.toString().contains('图片')) {
        _addLog('🔍 尝试简单图片处理方法...');
        try {
          await _uploadImageSimple();
        } catch (simpleError) {
          _addLog('❌ 简单处理方法也失败: $simpleError');
        }
      }
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  /// 生成AWS S3预签名URL
  Future<String> _generatePresignedUrl(String objectKey) async {
    // 实现AWS S3 Signature V4算法
    final now = DateTime.now().toUtc();
    final amzDate = now.toIso8601String().replaceAll(RegExp(r'[:\-]|\.\d{3}'), '');
    final dateStamp = amzDate.substring(0, 8);
    
    _addLog('🔍 时间戳: $amzDate');
    _addLog('🔍 日期戳: $dateStamp');

    // 构建查询参数
    final credentialScope = '$dateStamp/$_region/s3/aws4_request';
    final credential = '$_accessKeyId/$credentialScope';
    
    final queryParams = {
      'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
      'X-Amz-Credential': credential,
      'X-Amz-Date': amzDate,
      'X-Amz-Expires': '3600',
      'X-Amz-SignedHeaders': 'host',
    };

    // 构建规范查询字符串
    final canonicalQueryString = queryParams.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    // 构建规范请求
    final host = _endpoint.replaceAll(RegExp(r'^https?://'), '');
    final canonicalUri = '/$_bucketName/$objectKey';
    final canonicalHeaders = 'host:$host\n';
    const signedHeaders = 'host';
    const payloadHash = 'UNSIGNED-PAYLOAD';

    final canonicalRequest = [
      'PUT',
      canonicalUri,
      canonicalQueryString,
      canonicalHeaders,
      signedHeaders,
      payloadHash,
    ].join('\n');

    _addLog('🔍 规范请求构建完成');
    // 额外的详细信息直接打印到控制台
    debugPrint('🔍 [R2测试] 规范URI: $canonicalUri');
    debugPrint('🔍 [R2测试] 规范查询字符串: $canonicalQueryString');
    debugPrint('🔍 [R2测试] 完整规范请求:\n$canonicalRequest');

    // 构建待签名字符串
    final hashedCanonicalRequest = sha256.convert(utf8.encode(canonicalRequest)).toString();
    final stringToSign = [
      'AWS4-HMAC-SHA256',
      amzDate,
      credentialScope,
      hashedCanonicalRequest,
    ].join('\n');

    _addLog('🔍 待签名字符串构建完成');
    // 额外的详细信息直接打印到控制台
    debugPrint('🔍 [R2测试] 规范请求哈希: $hashedCanonicalRequest');
    debugPrint('🔍 [R2测试] 凭据范围: $credentialScope');
    debugPrint('🔍 [R2测试] 完整待签名字符串:\n$stringToSign');

    // 计算签名
    final signature = _calculateSignature(stringToSign, dateStamp);
    _addLog('🔍 签名计算完成: ${signature.substring(0, 16)}...');
    // 完整签名信息直接打印到控制台
    debugPrint('🔍 [R2测试] 签名计算完成: $signature');
    debugPrint('🔍 [R2测试] 签名长度: ${signature.length}');

    // 构建最终URL
    final finalUrl = '$_endpoint/$_bucketName/$objectKey?$canonicalQueryString&X-Amz-Signature=$signature';

    debugPrint('🔍 [R2测试] 最终预签名URL: $finalUrl');

    return finalUrl;
  }

  /// 计算AWS S3签名
  String _calculateSignature(String stringToSign, String dateStamp) {
    debugPrint('🔍 [R2测试] 开始HMAC-SHA256链式计算');

    // HMAC-SHA256链式计算
    var key = utf8.encode('AWS4$_secretAccessKey');
    debugPrint('🔍 [R2测试] 初始密钥长度: ${key.length}');

    // kDate = HMAC-SHA256(dateStamp, "AWS4" + secretAccessKey)
    var kDate = Hmac(sha256, key).convert(utf8.encode(dateStamp)).bytes;
    debugPrint('🔍 [R2测试] kDate: ${kDate.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');

    // kRegion = HMAC-SHA256(region, kDate)
    var kRegion = Hmac(sha256, kDate).convert(utf8.encode(_region)).bytes;
    debugPrint('🔍 [R2测试] kRegion: ${kRegion.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');

    // kService = HMAC-SHA256("s3", kRegion)
    var kService = Hmac(sha256, kRegion).convert(utf8.encode('s3')).bytes;
    debugPrint('🔍 [R2测试] kService: ${kService.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');

    // kSigning = HMAC-SHA256("aws4_request", kService)
    var kSigning = Hmac(sha256, kService).convert(utf8.encode('aws4_request')).bytes;
    debugPrint('🔍 [R2测试] kSigning: ${kSigning.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');

    // signature = HMAC-SHA256(stringToSign, kSigning)
    var signature = Hmac(sha256, kSigning).convert(utf8.encode(stringToSign));
    debugPrint('🔍 [R2测试] 最终签名: ${signature.toString()}');

    return signature.toString();
  }

  /// 上传到R2
  Future<bool> _uploadToR2(String presignedUrl, Uint8List fileBytes) async {
    try {
      _addLog('🔍 发送PUT请求到R2...');
      _addLog('🔍 文件大小: ${fileBytes.length} bytes');
      // 额外的详细信息直接打印到控制台
      debugPrint('🔍 [R2测试] 预签名URL: ${presignedUrl.substring(0, 100)}...');
      
      final response = await http.put(
        Uri.parse(presignedUrl),
        headers: {
          'Content-Type': 'image/jpeg',
          'Content-Length': fileBytes.length.toString(),
        },
        body: fileBytes,
      );

      _addLog('🔍 响应状态码: ${response.statusCode}');
      _addLog('🔍 响应头: ${response.headers}');

      if (response.body.isNotEmpty) {
        _addLog('🔍 响应体: ${response.body}');
      }

      return response.statusCode == 200;
    } catch (e) {
      _addLog('❌ 上传请求异常: $e');
      return false;
    }
  }

  /// 显示上传的图片
  void _showUploadedImage() {
    if (_uploadedImageUrl == null) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppBar(
              title: const Text('上传的图片'),
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Image.network(
                _uploadedImageUrl!,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return const Text('图片加载失败');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 添加日志
  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    final logMessage = '[$timestamp] $message';
    setState(() {
      _logs.add(logMessage);
    });
    // 同时打印到控制台
    debugPrint('[R2测试] $logMessage');
  }

  /// 清除日志
  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }
}
