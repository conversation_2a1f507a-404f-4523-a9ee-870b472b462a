import 'package:flutter/material.dart';
import '../services/service_locator.dart';

/// 自动登录测试页面
/// 用于测试和管理自动登录功能
class AutoLoginTestPage extends StatefulWidget {
  const AutoLoginTestPage({super.key});

  @override
  State<AutoLoginTestPage> createState() => _AutoLoginTestPageState();
}

class _AutoLoginTestPageState extends State<AutoLoginTestPage> {
  bool _autoLoginEnabled = false;
  Map<String, String?> _savedLoginInfo = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAutoLoginInfo();
  }

  Future<void> _loadAutoLoginInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final autoLoginEnabled = await Services.auth.isAutoLoginEnabled();
      final savedInfo = await Services.auth.getSavedLoginInfo();

      setState(() {
        _autoLoginEnabled = autoLoginEnabled;
        _savedLoginInfo = savedInfo;
      });
    } catch (e) {
      debugPrint('加载自动登录信息失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleAutoLogin(bool enabled) async {
    try {
      await Services.auth.setAutoLoginEnabled(enabled);
      await _loadAutoLoginInfo();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(enabled ? '自动登录已启用' : '自动登录已禁用'),
            backgroundColor: enabled ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      debugPrint('切换自动登录失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('操作失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _clearCredentials() async {
    try {
      await Services.auth.clearCredentials();
      await _loadAutoLoginInfo();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('已清除所有保存的凭据'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      debugPrint('清除凭据失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('自动登录测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 当前登录状态
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '当前登录状态',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text('已登录: ${Services.auth.isLoggedIn ? "是" : "否"}'),
                          if (Services.auth.currentUser != null) ...[
                            Text('用户名: ${Services.auth.currentUser!.username}'),
                            Text('邮箱: ${Services.auth.currentUser!.email}'),
                          ],
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 自动登录设置
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '自动登录设置',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          SwitchListTile(
                            title: const Text('启用自动登录'),
                            subtitle: const Text('下次启动应用时自动登录'),
                            value: _autoLoginEnabled,
                            onChanged: _toggleAutoLogin,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 保存的登录信息
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '保存的登录信息',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          if (_savedLoginInfo.isEmpty)
                            const Text('无保存的登录信息')
                          else ...[
                            if (_savedLoginInfo['email'] != null)
                              Text('邮箱: ${_savedLoginInfo['email']}'),
                            if (_savedLoginInfo['phone'] != null)
                              Text('手机号: ${_savedLoginInfo['phone']}'),
                            Text('自动登录: ${_savedLoginInfo['autoLoginEnabled']}'),
                          ],
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // 操作按钮
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _loadAutoLoginInfo,
                          child: const Text('刷新信息'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _clearCredentials,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('清除凭据'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // 说明文字
                  Card(
                    color: Colors.blue.shade50,
                    child: const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '使用说明：',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 8),
                          Text('1. 在登录页面勾选"记住我"选项'),
                          Text('2. 成功登录后，凭据会自动保存'),
                          Text('3. 下次启动应用时会自动登录'),
                          Text('4. 可以在此页面管理自动登录设置'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
