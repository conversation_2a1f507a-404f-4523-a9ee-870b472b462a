// Cloudflare Workers解决方案：生成R2预签名URL
// 这个Worker可以替代PocketBase的签名生成，使用标准的Web Crypto API

import { AwsClient } from 'aws4fetch';

export default {
  async fetch(request, env) {
    // 处理CORS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    }

    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    try {
      const { spotId, fileName, fileType } = await request.json();

      if (!spotId || !fileName || !fileType) {
        return new Response(JSON.stringify({
          success: false,
          error: '缺少必要参数: spotId, fileName, fileType'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // R2配置（从环境变量获取）
      const config = {
        accessKeyId: env.R2_ACCESS_KEY_ID,
        secretAccessKey: env.R2_SECRET_ACCESS_KEY,
        accountId: env.R2_ACCOUNT_ID,
        bucketName: env.R2_BUCKET_NAME,
        region: 'auto' // R2使用'auto'作为region
      };

      // 创建AWS客户端
      const client = new AwsClient({
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
        service: 's3',
        region: config.region,
      });

      // 生成对象键
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const objectKey = `spots/${spotId}/${randomId}/${timestamp}_${randomId}.${fileName.split('.').pop()}`;

      // 构建R2 URL
      const r2Url = `https://${config.accountId}.r2.cloudflarestorage.com/${config.bucketName}/${objectKey}`;

      // 生成预签名URL（PUT方法，1小时过期）
      const signedRequest = await client.sign(
        new Request(r2Url, {
          method: 'PUT',
        }),
        {
          aws: {
            signQuery: true, // 使用查询字符串签名
          },
        }
      );

      // 设置过期时间（1小时）
      const signedUrl = new URL(signedRequest.url);
      signedUrl.searchParams.set('X-Amz-Expires', '3600');

      // 重新签名包含过期时间的URL
      const finalSignedRequest = await client.sign(
        new Request(signedUrl.toString(), {
          method: 'PUT',
        }),
        {
          aws: {
            signQuery: true,
          },
        }
      );

      const response = {
        success: true,
        data: {
          uploadUrl: finalSignedRequest.url,
          objectKey: objectKey,
          method: 'PUT',
          headers: {
            'Content-Type': fileType,
          },
          expiresIn: 3600,
          generatedAt: new Date().toISOString()
        }
      };

      return new Response(JSON.stringify(response), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });

    } catch (error) {
      console.error('预签名URL生成失败:', error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error.message || '预签名URL生成失败'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      });
    }
  },
};

// 部署说明：
// 1. 在Cloudflare Dashboard中创建一个新的Worker
// 2. 将此代码复制到Worker编辑器中
// 3. 安装aws4fetch依赖：在Worker设置中添加npm包
// 4. 设置环境变量：
//    - R2_ACCESS_KEY_ID: 您的R2访问密钥ID
//    - R2_SECRET_ACCESS_KEY: 您的R2秘密访问密钥
//    - R2_ACCOUNT_ID: 您的Cloudflare账户ID
//    - R2_BUCKET_NAME: 您的R2存储桶名称
// 5. 部署Worker并获取Worker URL
// 6. 在Flutter应用中调用Worker URL而不是PocketBase API
